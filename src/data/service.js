const async = require('async');
const ms = require('ms')
import {environment} from '../config';

const Service = new mongoose.Schema({
    icon: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    screen: {
      type: String
    },
    config: {
      type: mongoose.Schema.Types.Mixed
    },
    region: {
      type: mongoose.Schema.Types.Mixed
    },
    platform: {
      type: mongoose.Schema.Types.Mixed
    },
    platformDriver: {
      type: mongoose.Schema.Types.Mixed
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
});

Service.statics.list = function (region, platform, nativeVersion, appName, cb) {
  appName = appName || 'heyu'
  const version = Number(nativeVersion);

  const query = {
    open: 1,
    // appName: {
    //   '$exists': false
    // },
    $or: [
      {
        'region.allow': region
      },
      {
        'region.allow': 'all',
        'region.deny': {
          $ne: region
        }
      }
    ]
  };

  if (appName && appName !== 'driver' && appName !== 'staff') {
    query.appName = appName;
  }
  if(appName === 'driver') {
    query[`platformDriver.${platform}.from`] = {$lte: version};
    query[`platformDriver.${platform}.to`] = {$gte: version};
    query[`platformDriver.${platform}.deny`] = {$ne: version};
  } else if (appName === 'staff') {
    // query[`platformStaff.${platform}.from`] = {$lte: version};
    // query[`platformStaff.${platform}.to`] = {$gte: version};
    // query[`platformStaff.${platform}.deny`] = {$ne: version};
  } else {
    query[`platform.${platform}.from`] = {$lte: version};
    query[`platform.${platform}.to`] = {$gte: version};
    query[`platform.${platform}.deny`] = {$ne: version};
  }


  let func = this.find(query, "-createdAt -updatedAt -region -open -order -platform -showListShipper").lean()

  if (environment === 'production') {
    func = func.cache(ms('15m')/1000, `service:${region}:${platform}:${version}`)
  }

  func
    .sort('-order')
    .exec(cb)
}

// module.exports = mongoose.model('Service', Service);
module.exports = mongoConnections('master').model('Service', Service);
