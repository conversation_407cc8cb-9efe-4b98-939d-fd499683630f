import { Router } from 'express';
import moment from 'moment';
import _ from 'lodash';
import * as FB from 'fb';
import * as async from 'async';
import uuid from 'uuid/v4';
import CONSTANTS from '../../const';
import message from '../../message';
import rp from 'request-promise'
import ms from 'ms';
import * as config from '../../config';
import LocationManager from '../../task/updateLocation';
import {proxyFacebookServerAddr, codePhoneAddr, SANSHIP_FB_APPID, authenServiceAddr, service, notifyServiceAddr} from '../../config';
import request from 'request';
import bcrypt from 'bcrypt';
import isMobilePhone from 'validator/lib/isMobilePhone';
import * as helpers from '../../helpers';
import * as mailUtil from '../../mailUtil';
const generate = require('nanoid/generate')


export default {
  chooseMode(req, res) {
    const type = _.get(req, 'body.type', '');      // 0 means shipper, 2 means shop, 1 means admin, 3: merchant, 4: staff
    const userId = req.user.id;
    const appName = _.get(req, 'body.appName', '')

    Members
      .findOneAndUpdate({
        _id: userId
      }, {
        type,
        appName: appName ? appName : 'customer'
      })
      .exec((err, result) => {
        if(err || !result) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }

        if(!_.has(result, 'type') || result.type === -1) {
          const objPublish = {
            userId,
            type
          }

          redisConnectionForPubSub.publish("user_choose_mode", JSON.stringify(objPublish));
        }

        if(type === 0 && (!appName || (appName && appName !== 'driver'))) {

          let stringUser = `driver:${userId}`


          redisConnection.get(stringUser, (err, token) => {

            if(token) {

              let stringToken = `driver:${token}`

              redisConnection.del(stringToken, (err, result) => {});
            }
          });
        }

        res.json({
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  },
  blockUser(req, res) {
    const userId = req.body.userId;
    const blockUtil = req.body.blockUtil || (Date.now() + ms('1h'));

    let userInf;
    const checkParams = (next) => {
      if(!userId) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        })
      }

      next();
    }

    const updateBlock = (next) => {
      Members
        .findOneAndUpdate({
          _id: userId
        }, {
          blockUtil: blockUtil
        })
        .exec((err, result) => {
          if(err || !result) {
            return next(err || new Error(`Not found user inf`))
          }

          userInf = result;

          next();
        })
    }

    const deleteToken = (next) => {
      redisConnection.get(`user:${userId}`, (err, token) => {
        if(err) {
          return next(err);
        }

        if(token) {
          redisConnection
            .multi()
            .del(`user:${userId}`)
            .del(`user:${token}`)
            .exec((err, result) => {
              if(err) {
                return next(err);
              }

              next(null, {
                code: CONSTANTS.CODE.SUCCESS
              })
            })
        } else {
          next(null, {
            code: CONSTANTS.CODE.SUCCESS
          })
        }
      })
    }

    async.waterfall([
      checkParams,
      updateBlock,
      deleteToken
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: message.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },
  checkExistsByPhone(req, res) {
    let phone = _.get(req, 'body.phone', '');
    phone = phone.trim();
    if(!phone) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: message.SYSTEM.ERROR
      })
    }

    const aliasPhone = helpers.getAliasPhone(phone);
    const aliasPhone1 = helpers.convertToNewPhone(phone);

    if(aliasPhone1) {
      return res.json({
        code: CONSTANTS.CODE.FAIL,
        data: {
          count: 5,
          hasPassword: 0
        },
        message:{
          'head': 'Thông báo',
          'body': `Vui lòng đăng nhập bằng đầu số mới ${aliasPhone1}. Nếu có vấn đề nào chưa rõ vui lòng liên hệ: 1900.633.689. Xin cảm ơn.`
        }
      })
    }

    let query = {
      phone
    }

    if(aliasPhone || aliasPhone1) {
      query = {
        '$or': [
          {
            phone
          },
          {
            phone: aliasPhone || aliasPhone1
          }
        ]
      }
    }

    Members
      .find(query, "password")
      .lean()
      .exec((err, results) => {
        if(err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: message.SYSTEM.ERROR
          })
        }

        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            count: results.length,
            hasPassword: (results.length === 1 && results[0].password) ? 1 : 0
          },
          message: results.length > 1 ? {
            'head': 'Thông báo',
            'body': 'Số điện thoại này được đăng ký cho nhiều hơn 1 tài khoản vui lòng liên hệ 1900.633.689 để được hỗ trợ. Xin cảm ơn.'
          } : undefined
        })
      });
  },
  updateProfile(req, res) {
    const userId = req.user.id;
    const name = req.body.name || '';
    const email = req.body.email || '';
    const birthday = req.body.birthday || '';
    const picture = req.body.picture || '';
    const access_token = req.body.access_token || '';
    const address = req.body.address || '';

    let shouldMergeFb = false;
    let userInf;
    let infoFromToken;

    const checkParams = (next) => {
      if(!name) {
        return res.json({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: message.SYSTEM.ERROR
        })
      }

      next();
    }

    const getUserInfo = (next) => {
      Members
        .findById(userId)
        .lean()
        .exec((err, result) => {
          if(err || !result) {
            return next(err || new Error(`Not found user info`));
          }

          userInf = result;

          next();
        });
    }

    const preventChangeNameWhenAuthened = (next) => {
      if((_.get(userInf, 'ship.isAuthen', 0) || _.get(userInf, 'staff.isAuthen', 0)) && userInf.name.trim() !== name.trim()) {
        return next({
          code: CONSTANTS.CODE.FAIL,
          message: message.USER.CAN_NOT_CHANGE_NAME
        })
      }

      if(_.get(userInf, 'staff.isAuthen', 0) && _.get(userInf, 'facebook.birthday', '').trim() !== birthday.trim()) {
        return next({
          code: CONSTANTS.CODE.FAIL,
          message: message.USER.CAN_NOT_CHANGE_BIRTHDAY
        })
      }

      next();
    }

    const getInfoFromToken = (next) => {
      if(!access_token) {
        return next();
      }

      const options = {
        method: 'POST',
        uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-profile`,
        body: {
          access_token
        },
        json: true // Automatically stringifies the body to JSON
      };

      request(options, (err, response, result) => {
        if(err || !response || response.statusCode !== 200 || !result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          })
        }

        if(result.code !== CONSTANTS.CODE.SUCCESS) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          })
        }

        infoFromToken = result.userInfo;
        next(null);
      })
    }

    const checkCanMergeFB = (next) => {
      if(!access_token || userInf.facebook.id) {
        return next();
      }

      return next();

      Members
        .count({
          'facebook.id': infoFromToken.id
        })
        .exec((err, count) => {
          if(!err && count === 0) {
            shouldMergeFb = true;
          }

          next();
        })
    }

    const updateUserInf = (next) => {
      let objUpdate = {};

      if (name.trim() && !_.get(userInf, 'ship.isAuthen', 0) && !_.get(userInf, 'staff.isAuthen', 0)) {
        objUpdate.name = name.trim();
      }

      if (email.trim()) {
        objUpdate.email = email.trim();
      }

      if (birthday.trim() && !_.get(userInf, 'staff.isAuthen', 0)) {
        objUpdate.birthday = birthday.trim();
      }

      if (address.trim()) {
        objUpdate.address = address.trim();
      }

      if (picture.trim() && !_.get(userInf, 'ship.isAuthen', 0) && !_.get(userInf, 'staff.isAuthen', 0)) {
        objUpdate['facebook.picture'] = picture.trim();
      }

      if(infoFromToken && !userInf.facebook.id) {
        objUpdate['facebook.name'] = infoFromToken.name;
        objUpdate['facebook.email'] = infoFromToken.email || '';
        objUpdate['facebook.birthday'] = infoFromToken.birthday || '';
        objUpdate['facebook.picture'] = picture || infoFromToken.picture;
        objUpdate['facebook.token'] = access_token;
        if(shouldMergeFb) {
          objUpdate['facebook.id'] = infoFromToken.id;
        }
      } else if(!userInf.facebook.token) {
        if (name.trim() && !_.get(userInf, 'ship.isAuthen', 0) && !_.get(userInf, 'staff.isAuthen', 0)) {
          objUpdate['facebook.name'] = name.trim();
        }
        objUpdate['facebook.email'] = email.trim();
        if (birthday.trim() && !_.get(userInf, 'staff.isAuthen', 0)) {
          objUpdate['facebook.birthday'] = birthday.trim();
        }
        if (picture.trim() && !_.get(userInf, 'ship.isAuthen', 0) && !_.get(userInf, 'staff.isAuthen', 0)) {
          objUpdate['facebook.picture'] = picture.trim();
        }
      }

      Members
        .findOneAndUpdate({
          _id: req.user.id
        }, objUpdate, {
          new:true
        })
        .exec((err, result) => {
          if(err) {
            return next(err);
          }
          ProfileLog
            .create({
              member: req.user.id,
              oldInf: userInf,
              updateInf: result
            },(error,data) =>{
            })
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS
          })
        })
    }

    async.waterfall([
      checkParams,
      getUserInfo,
      preventChangeNameWhenAuthened,
      getInfoFromToken,
      checkCanMergeFB,
      updateUserInf
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },
  updateLocation(req, res) {
    res.json({
      code: CONSTANTS.CODE.SUCCESS
    });

    const platform = req.query.platform || '';
    const userId = req.user.id;

    req.body.forEach((locationData) => {
      const objCreate = {
        member: userId,
        location: {
          type: 'Point',
          coordinates: [
            locationData.lng,
            locationData.lat
          ]
        },
        bearing: locationData.bearing,
        speed: locationData.speed,
        updatedAt: Date.now()
      }

      Location
        .create(objCreate, () => {})

      if(platform && platform === 'android') {
        Members
          .findOneAndUpdate(
            {_id:userId},
            {
              location:{
                type:'Point',
                coordinates:[
                  locationData.lng,
                  locationData.lat
                ]
              },
              updatedAt:Date.now()
            }
          )
          .lean()
          .exec((err, member) => {
        });
      }

      // Publish via redis
      const objPublish = {
        user: userId,
        locationData: locationData
      }

      redisConnectionForPubSub.publish("location_update", JSON.stringify(objPublish));
    })
  },
  syncLocationFail(req, res) {
    res.json({
      code: CONSTANTS.CODE.SUCCESS
    });

    const userId = req.user.id;

    req.body.forEach((locationData) => {
      const objCreate = {
        member: userId,
        location: {
          lat: locationData.lat,
          lng: locationData.lng
        },
        sync: 1,
        updatedAt: locationData.updatedAt || Date.now()
      }

      Location
        .create(objCreate, () => {})
    })
  },
  resetPassword(req, res) {
    const phone = req.body.phone;
    const newPassword = req.body.newPassword;
    const token = req.body.token;
    const code = req.body.code;

    let passwordHashed = '';
    let memberToken = '';
    let userInfDB;
    let memberExists = false

    const checkParams = (next) => {
      if(!phone || newPassword.length < 6 || !token) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: message.SYSTEM.ERROR
        });
      }
      next(null);
    }

    const getPhoneFromToken = (next) => {
      const options = {
        method: 'POST',
        uri: `${codePhoneAddr}/api/v1.0/check-code`,
        body: {
            token, code, phone
        },
        json: true // Automatically stringifies the body to JSON
      };

      request(options, (err, response, result) => {
        if(err){
          mailUtil
            .sendMail(` --- ERR resetPassword ${codePhoneAddr}/api/v1.0/check-code --- ${err}`);
        }
        if(err || !response || response.statusCode !== 200 || !result || result.code !== 200) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          })
        }

        next();
      })
    }

    const hashPassword = (next) => {
      bcrypt.hash(newPassword, 10, function(err, hash) {
        if(err) {
          return next(err);
        }

        passwordHashed = hash;
        next();
      });
    }

    const checkMemberExists = (next) => {
      memberToken = uuid();
      Members
        .findOne({
          phone: phone
        })
        .lean()
        .exec((err,result) => {
          if(err) {
            return next(err)
          }
          memberExists = result ? true : false
          next();
        })
    }

    const createIfExists = (next) => {
      if(memberExists) {
        return next()
      }
      Members
        .create({
          phone: phone,
          password: passwordHashed,
          status: 1
        },(err, result) => {
          if(err) {
            return next(err)
          }
          if(!result) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }
          userInfDB = result.toObject();
          next();
        })
    }

    const updatePassword = (next) => {
      if(!memberExists) {
        return next();
      }
      const options = {
        'new': true,
        upsert: true,
        setDefaultsOnInsert: true
      };

      Members
        .findOneAndUpdate({
            phone: phone
        }, {
          password: passwordHashed,
          status: 1
        }, options)
        .lean()
        .exec((err, result) => {
            if(err || !result) {
              return next(err || new Error(`Not found user with that phone`));
            }

            if(result.blockUtil > Date.now()) {
              ReasonBlock
                .find({member: result._id})
                .sort({"createdAt": -1})
                .limit(1)
                .lean()
                .exec((err, resultBlock) => {
                  if(err) {
                    return next({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                    });
                  }

                  let messageBlock;
                  if(resultBlock.length === 1 && resultBlock[0].message) {
                    messageBlock = {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                    }
                  } else {
                    messageBlock = {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                    }
                  }

                  return next({
                    code: CONSTANTS.CODE.FAIL,
                    message: messageBlock
                  })
                })

              return;
            }

            userInfDB = result;
            next();
        });
    }

    const handleRegisterService = (next) => {
      OrderType
        .find({move: {$ne: 1}, hireDriver: {$ne:1}}, '_id')
        .lean()
        .exec((err, result) => {
          if (err || !result) {
            return next(err || new Error(`Not found order type info`));
          }

          const orderTypeId = result.map(orderType => orderType._id.toHexString());
          Rider.count({member: userInfDB._id}, (err, count) => {
            if (count > 0) {
              return next();
            }

            Rider.create({
              member: userInfDB._id,
              serviceRunning: service.order.concat(orderTypeId)
            }, (err, result) => {
              if(err) {
                return next(err);
              }
              next();
            })
          })
        })
    }

    const makeSingleLogin = (next) => {
        const userId = userInfDB._id.toHexString();
        redisConnection.get(`user:${userId}`, (err, token) => {
            if(err) {
                return next(err);
            }

            if(token) {
                redisConnection.del(`user:${token}`, (err, result) => {
                    if(err) {
                        return next(err);
                    }

                    next();
                });
            } else {
                next();
            }
        });
    }

    const generateToken = (next) => {
        const userId = userInfDB._id.toHexString();
        const objSign = {
            id: userId,
            accessToken: _.get(req, 'body.access_token', '')
        }

        redisConnection
            .multi()
            .set(`user:${userId}`, memberToken)
            .set(`user:${memberToken}`, JSON.stringify(objSign))
            .exec((err, result) => {
                if(err) {
                    return next(err);
                }

                const data = _.merge({}, userInfDB, {memberToken});
                _.unset(data, 'facebook.token');
                _.unset(data, 'password');
                data.isExpire = (data.expireTime < Date.now());

                // publish event
                if(Date.now() - data.createdAt <= 30000) {
                  redisConnectionForPubSub.publish("new_user", data._id.toHexString());
                }

                next(null, {
                  code: CONSTANTS.CODE.SUCCESS,
                  member: data
                });
            });
    }

    async.waterfall([
      checkParams,
      getPhoneFromToken,
      hashPassword,
      checkMemberExists,
      createIfExists,
      updatePassword,
      handleRegisterService,
      makeSingleLogin,
      generateToken
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
      });

      res.json(data || err);
    });
  },
  changePhoneForUser(req, res) {
    const userId = req.user.id;
    const token = req.body.token || '';
    const code = req.body.code || '';
    let newPhone = _.get(req, 'body.newPhone', '');
    let isOpenChangePhoneForRider = 0
    let userInf;

    const checkParams = (next) => {
      newPhone = newPhone.replace(/\s/g, '');

      if(!newPhone) {
        return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
        });
      }

      next();
    }

    const getPhoneFromToken = (next) => {
      const options = {
        method: 'POST',
        uri: `${codePhoneAddr}/api/v1.0/check-code`,
        body: {
            token, code, phone: newPhone
        },
        json: true // Automatically stringifies the body to JSON
      };

      request(options, (err, response, result) => {
        if(err) {
          mailUtil
            .sendMail(` --- ERR changePhoneForUser ${codePhoneAddr}/api/v1.0/check-code --- ${err}`);
        }
        if(err || !response || response.statusCode !== 200 || !result || result.code !== 200) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          })
        }

        next();
      })
    }

    const checkExistPhone = (next) => {
      if(helpers.convertToNewPhone(newPhone)) {
        return next({
          code: CONSTANTS.CODE.FAIL,
          message: {
            head: 'Cập nhật đầu số',
            body: `Rất xin lỗi bạn số điện thoại bạn cần đổi hiện tại đã được chuyển thành số mới: ${helpers.convertToNewPhone(newPhone)}. Vui lòng thử thay đổi lại với số mới. Xin cảm ơn.`
          }
        })
      }

      Members
        .count({
          phone: newPhone
        })
        .exec((err, count) => {
          if(err) {
            return next(err);
          }

          if(count > 0) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: message.USER.EXIST_PHONE
            })
          }

          next();
        });
    }

    const getCurrentPhone = (next) => {
      Members
        .findById(userId, "phone ship")
        .lean()
        .exec((err, result) => {
          if(err || !result) {
            return next(err || new Error(`Not found user info`));
          }

          userInf = result;

          next();
        })
    }

    const getConfigChangePhone = (next) => {
      Config
        .get(CONSTANTS.CONFIG_TYPE.CHANGE_PHONE, req.body.regionName, (err, data) => {
          if (err) {
            return next(err)
          }

          isOpenChangePhoneForRider = data && data.config && data.config.isOpen ? data.config.isOpen : 0
          next();
        })
    }

    const checkIsNotShipperAuthen = (next) => {
      if(userInf.ship.isAuthen && !isOpenChangePhoneForRider) {
        return next({
          code: CONSTANTS.CODE.FAIL,
          message: message.USER.NOT_ALLOW_TO_CHANGE_PHONE
        })
      }

      next();
    }

    const writeLog = (next) => {
      ChangePhoneLog
        .create({
          member: userId,
          oldPhone: userInf.phone,
          newPhone: newPhone
        }, (err, result) => {
          if(err) {
            return next(err);
          }

          next();
        })
    }

    const updateUserInf = (next) => {
      Members
        .update({
          _id: userId
        }, {
          phone: newPhone
        })
        .exec((err, result) => {
          if(err) {
            return next(err);
          }

          next(null , {
            code: CONSTANTS.CODE.SUCCESS,
            phone: newPhone,
            message: message.USER.UPDATE_PHONE
          });
        })
    }

    async.waterfall([
      checkParams,
      getPhoneFromToken,
      checkExistPhone,
      getCurrentPhone,
      getConfigChangePhone,
      checkIsNotShipperAuthen,
      writeLog,
      updateUserInf
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: message.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },
  addDefaultLocation(req, res) {
    const nameSender = req.body.nameSender || '';
    const phoneSender = req.body.phoneSender || '';

    const userId = req.user.id;
    if(!req.body.location || !req.body.name || !req.body.location.lat || !req.body.location.lng) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    let objLocation = {};
    objLocation.location = req.body.location;
    objLocation.name = req.body.name;
    objLocation.member = req.user.id;
    objLocation.nameDefault = req.body.nameDefault || '';
    objLocation.subNameDefault = req.body.subNameDefault || '';
    objLocation.nameSender = req.body.nameSender || '';
    objLocation.phoneSender = req.body.phoneSender || '';
    objLocation.nameMain = req.body.nameMain || '';
    objLocation.nameSecondary = req.body.nameSecondary || '';
    DefaultLocation
      .create(objLocation, (err, result) => {
        if(err) {
          return res.json({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }
        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  },
  removeDefaultLocation(req,res) {

    const locationId = req.body.id || '';

    if(!req.body.id) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    DefaultLocation
      .findOneAndUpdate(
        {
          _id: locationId,
          status: 1
        },
        {
          status:0,
          updatedAt:Date.now()
        }
      )
      .lean()
      .exec((err, result) => {
        if(err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }
        res.json({
          code: CONSTANTS.CODE.SUCCESS
        })
      });
  },
  modifyDefaultLocation(req,res) {

    const locationId = req.body.id || '';
    const name = req.body.name || '';
    const nameDefault = req.body.nameDefault || '';
    const subNameDefault = req.body.subNameDefault || '';
    const location = req.body.location || '';
    const nameSender = req.body.nameSender || '';
    const phoneSender = req.body.phoneSender || '';
    const nameMain = req.body.nameMain || '';
    const nameSecondary = req.body.nameSecondary || '';

    if(!locationId) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    let objUpdate = {
      updatedAt:Date.now(),
      subNameDefault: subNameDefault
    };
    if(name) {
      objUpdate.name = name
    }
    if(location) {
      objUpdate.location = location
    }

    if (nameSender) {
      objUpdate.nameSender = nameSender;
    }

    if (phoneSender) {
      objUpdate.phoneSender = phoneSender;
    }

    if (nameDefault) {
      objUpdate.nameDefault = nameDefault;
    }

    if (nameMain) {
      objUpdate.nameMain = nameMain;
    }

    if (nameSecondary) {
      objUpdate.nameSecondary = nameSecondary;
    }

    DefaultLocation
      .findOneAndUpdate(
        {
          _id: locationId,
          status: 1
        },
        objUpdate
      )
      .lean()
      .exec((err, result) => {
        if(err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }
        res.json({
          code: CONSTANTS.CODE.SUCCESS
        })
      });
  },
  unshiftDefaultLocation(req,res) {

    const locationId = req.body.id || '';

    DefaultLocation
      .findOneAndUpdate(
        {
          _id: locationId,
          status: 1
        },
        {
          createdAt: Date.now()
        }
      )
      .lean()
      .exec((err, result) => {
        if(err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }
        res.json({
          code: CONSTANTS.CODE.SUCCESS
        })
      });
  },
  listDefaultLocation(req,res) {
    const userId = req.user.id || '';
    DefaultLocation
      .find({
        member: userId,
        status:1
      })
      .sort({
        createdAt: -1
      })
      .limit(30)
      .lean()
      .exec((err, result) => {
        if(err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }
        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  },
  modifyListDefaultLocation(req, res) {
    const arr = req.body.newList || []
    async.eachSeries(arr, (item, callback) => {
        const locationId = item._id || ''
        const createdAt = item.createdAt || '';
        let objUpdate = {};
        if (createdAt) {
            objUpdate.createdAt = createdAt;
        }
        DefaultLocation
            .update(
                {
                    _id: locationId,
                    status: 1,
                },
                objUpdate
            )
            .lean()
            .exec((err, result) => {
                if (err) {
                    callback(err)
                }
                callback(null)
            });
    }, (err) => {
        if (err) {
            return res.json({
                code: CONSTANTS.CODE.SYSTEM_ERROR
            })
        }
        res.json({
            code: CONSTANTS.CODE.SUCCESS,
        })
    })
},
  getPhoneCode(req, res) {
    const phone = req.body.phone;
    const platform = req.body.platform
    const appName = req.body.appName || '';

    let users;
    const checkMultipleAccount = (next) => {
      Members
        .find({
          phone
        }, "blockUtil")
        .lean()
        .exec((err, results) => {
          if(err) {
            return next(err);
          }

          if(results.length > 1) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: message.USER.MULTIPLE_ACCOUNT
            })
          }

          users = results;

          next();
        })
    }

    const checkBlock = (next) => {
      let isBlock = false;
      for(let i=0; i < users.length; i++) {
        if(users[i].blockUtil - Date.now() >= 0) {
          isBlock = true;
          break;
        }
      }

      if(!isBlock) {
        return next();
      }

      let userInfDB = users[0];

      ReasonBlock
        .find({member: userInfDB._id})
        .sort({"createdAt": -1})
        .limit(1)
        .lean()
        .exec((err, resultBlock) => {
          if(err) {
            return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
            });
          }

          let messageBlock;
          if(resultBlock.length === 1 && resultBlock[0].message) {
            messageBlock = {
              head: 'Thông báo',
              body: `Tài khoản của bạn đã bị khoá tới ${moment(userInfDB.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
            }
          } else {
            messageBlock = {
              head: 'Thông báo',
              body: `Tài khoản của bạn đã bị khoá tới ${moment(userInfDB.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
            }
          }

          next({
            code: CONSTANTS.CODE.FAIL,
            message: messageBlock
          })
        })
    }

    const checkWowMember = (next) => {
      if(!appName || (appName && appName !== 'heywow')) {
        return next();
      }

      let query = {
        phone,
        status: 1
      }
      if(users.length) {
        query = {
          $or:[
            {phone: phone},
            {member: users[0]._id}
          ],
          status: 1
        }
      }
      HeyWowMember
        .count(query)
        .exec((err, count)=>{
          if(err){
            return next(err);
          }
          if(!count) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Số điện thoại của bạn chưa đủ điều kiện để sử dụng HeyWow. Xin cảm ơn'
              }
            })
          }
          next();
        })
    }

    const sendCode = (next) => {

      const options = {
        method: 'POST',
        uri: `${codePhoneAddr}/api/v2.0/send-code`,
        body: {
            phone: req.body.phone,
            ip: req.headers['x-forwarded-for'],
            deviceId: req.body.deviceId,
            platform
        },
        json: true // Automatically stringifies the body to JSON
      };

      if(platform === 'web') {
        options.body.ip = req.body.ip
      }

      rp(options)
        .then((result) => {
          next(null, result);
        })
        .catch((err) => {
          mailUtil
            .sendMail(` --- ERR ${codePhoneAddr}/api/v2.0/check-code --- ${err}`);

          next(err);
        });
    }

    async.waterfall([
      checkMultipleAccount,
      checkBlock,
      checkWowMember,
      sendCode
    ], (err, data) => {
      console.log('haha:err', err, data, appName)
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: message.SYSTEM.ERROR
      })

      res.json(data || err)
    })
  },

  get(req, res) { //apiget

    const userId = req.user.id
    const appName = req.body.appName || ''
    const hidePopup = req.body.hidePopup || false;
    let ttl = ms('5m');
    let timeoutRefresh = ms('30m')
    let bonusTimeout = ms('3h')
    let dataResponse = {}
    let heywowMember;
    let isPartnerHeyCare = 0;

    const getConfigRefreshToken = (next) => {
      ConfigRefreshToken
        .findOne({})
        // .cache(0, config.keyCache)
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err)
          }
          if(result) {
            ttl = ms(result.ttl);
            timeoutRefresh = ms(result.timeoutRefresh);
            bonusTimeout = ms(result.bonusTimeout);
          }
          next();
        })
    }

    const getHeywowInf = (next) => {
      if(appName !== 'heywow') {
        return next();
      }

      HeyWowMember
        .findOne({
          member: userId
        })
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err)
          }
          heywowMember = result

          next();
        })
    }

    const handleHeyCarePartner = (next) => {
      if(appName !== 'staff') {
        return next();
      }
      PartnerHeyCare
        .count({
          member: userId,
          status:1
        })
        .exec((err, count) => {
          if(err) {
            return next(err)
          }
          if(count) {
            isPartnerHeyCare = 1
          }
          next();
        })
    }

    const getMember = (next) => {

    Members
      .findById(userId, "-shop.postList -dislikedList -likedList -facebook.token -password -location -likes -dislikes -os_version -memberToken -address -birthday -presenterCode -updatedAt -v")
      .lean()
      .exec((err, result) => {
          if (err || !result) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
              })
          }

          if(appName === 'heywow' && heywowMember) {
            result.department = req.user.department;
            result.roles = req.user.roles
            result.totalOrder = heywowMember.totalOrder;
            result.membership = heywowMember.membership
          }

          result.isPartnerHeyCare = isPartnerHeyCare

          if(result.blockUtil > Date.now()) {
            ReasonBlock
              .find({member: req.user.id})
              .sort({"createdAt": -1})
              .limit(1)
              .lean()
              .exec((err, resultBlock) => {
                if(err) {
                  return next({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                  });
                }

                let messageBlock;
                if(resultBlock.length === 1 && resultBlock[0].message) {
                  messageBlock = {
                    head: 'Thông báo',
                    body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                  }
                } else {
                  messageBlock = {
                    head: 'Thông báo',
                    body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                  }
                }

                return next({
                  code: CONSTANTS.CODE.TOKEN_EXPIRE,
                  message: messageBlock
                })
              })

            return;
          }

          if(result.blockOrderUtil > Date.now()) {
            ReasonBlock
              .find({member: req.user.id})
              .sort({"createdAt": -1})
              .limit(1)
              .lean()
              .exec((err, resultBlock) => {
                if(err) {
                  return next({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                  });
                }

                let messageBlock;
                if(resultBlock.length === 1 && resultBlock[0].message) {
                  messageBlock = {
                    head: 'Thông báo',
                    body: `Tài khoản của bạn đã bị tạm ngưng nhận đơn hệ thống tới ${moment(result.blockOrderUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                  }
                } else {
                  messageBlock = {
                    head: 'Thông báo',
                    body: `Tài khoản của bạn đã bị tạm ngưng nhận đơn hệ thống tới ${moment(result.blockOrderUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                  }
                }

                result.isExpire = (result.expireTime < Date.now());
                result.memberToken = req.user.memberToken;
                if(hidePopup) {
                  dataResponse = {
                    code: CONSTANTS.CODE.SUCCESS,
                    member: result
                  }
                } else {
                  dataResponse = {
                    code: CONSTANTS.CODE.SUCCESS,
                    member: result,
                    message: messageBlock
                  }
                }


                next(null);
              })
          } else {
            result.isExpire = (result.expireTime < Date.now());
            result.memberToken = req.user.memberToken;

            dataResponse = {
              code: CONSTANTS.CODE.SUCCESS,
              member: result
            }

            next(null);
          }
        });
    }

    const handleToken = (next) => {

      let memberToken = req.user.memberToken;

      let stringUser = `user:${userId}`
      let stringToken = `user:${memberToken}`

      if (appName) {
        stringUser = `${appName}:${userId}`
        stringToken = `${appName}:${memberToken}`
      }

      redisConnection.get(stringToken, (err, result) => {
        if(err) {
            return next(err);
        }
        if(!result){
          return next({
            code: CONSTANTS.CODE.TOKEN_EXPIRE,
            message: message.USER.TOKEN_EXPIRE
          })
        }
        const objUser = JSON.parse(result);

        if(!objUser.refreshAt || ((Date.now() - objUser.refreshAt) > timeoutRefresh) && !objUser.willExpire) {

          const objUserOld = objUser;

          objUserOld.willExpire = true;
          const newToken = uuid();
          objUserOld.newToken = newToken
          redisConnection.setex(stringToken, ttl/1000, JSON.stringify(objUserOld), (error, res) => {
            if(error) {
              return next(error)
            }


            objUser.refreshAt = Date.now();
            objUser.memberToken = newToken;
            objUser.willExpire = false;

            let stringTokenNew = `user:${newToken}`

            if (appName) {
              stringTokenNew = `${appName}:${newToken}`
            }

            const fnc =
              redisConnection
                .multi()
                .set(stringTokenNew, JSON.stringify(objUser))
                .set(stringUser, newToken)

            fnc
              .exec((ez, data) => {
                if(ez) {
                  return next(ez);
                }

                dataResponse.member.memberToken = newToken
                dataResponse.member.isRefresh = true
                next(null,dataResponse)
              });

          });


        } else {
          if(objUser.willExpire && objUser.newToken) {
            dataResponse.member.memberToken = objUser.newToken
          }
          next(null,dataResponse)
        }
      });
    }

    async.waterfall([
      getConfigRefreshToken,
      getHeywowInf,
      handleHeyCarePartner,
      getMember,
      handleToken
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
      });

      res.json(data || err);
    });
  },
  loginWithoutPassword(req, res) {
    const phone = req.body.phone;
    const token = req.body.token;
    const code = req.body.code;
    const fromPartner = _.get(req, 'body.fromPartner', 0)
    const appName = _.get(req,'body.appName', '');
    const fromTickBox = req.body.fromTickBox || '';
    const deviceId = req.body.deviceId;
    let objWow = {}
    let isPartnerHeyCare = 0
    let memberResult;

    let memberToken = '';
    let userInfDB;
    let memberExists = false
    let tokenRedis;

    const checkParams = (next) => {

      if(phone && fromPartner) {
        return next();
      }

      if (!phone || !token) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: message.SYSTEM.ERROR
        });
      }
      next();
    }

    const getPhoneFromToken = (next) => {
      if(fromPartner) {
        return next();
      }
      const options = {
        method: 'POST',
        uri: `${codePhoneAddr}/api/v1.0/check-code`,
        body: {
            token, code, phone
        },
        json: true // Automatically stringifies the body to JSON
      };

      request(options, (err, response, result) => {
        if(err) {
          mailUtil
            .sendMail(` --- ERR ${codePhoneAddr}/api/v1.0/check-code --- ${err}`);
        }
        if(err || !response || response.statusCode !== 200 || !result || result.code !== 200) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          })
        }

        next();
      })
    }
    const checkMemberExists = (next) => {
      memberToken = uuid();
      Members
        .findOne({
          phone: phone
        })
        .lean()
        .exec((err,result) => {
          if(err) {
            return next(err)
          }
          memberExists = result ? true : false
          next();
        })
    }

    const createIfNotExists = (next) => {
      if(memberExists) {
        return next()
      }

      let objCreate = {
        phone: phone,
        status: 1
      }
      if(fromTickBox) {
        objCreate.fromTickBox = 1
      }

      Members
        .create(objCreate,(err, result) => {
          if(err) {
            return next(err)
          }
          if(!result) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }
          userInfDB = result.toObject();
          next();
        })
    }

    const updateIfExits = (next) => {
      if(!memberExists) {
        return next();
      }

      const options = {
        'new': true,
        upsert: true,
        setDefaultsOnInsert: true
      };

      Members
        .findOneAndUpdate({
          phone
        }, {
          status: 1
        }, options)
        .lean()
        .exec((err, result) => {
          if(err || !result) {
            return next(err || new Error(`Not found user with that phone`));
          }

          if(result.blockUtil > Date.now()) {
            ReasonBlock
              .find({member: result._id})
              .sort({"createdAt": -1})
              .limit(1)
              .lean()
              .exec((err, resultBlock) => {
                if(err) {
                  return next({
                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                    message: message.SYSTEM.ERROR
                  });
                }

                let messageBlock;
                if(resultBlock.length === 1 && resultBlock[0].message) {
                  messageBlock = {
                    head: 'Thông báo',
                    body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                  }
                } else {
                  messageBlock = {
                    head: 'Thông báo',
                    body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                  }
                }

                return next({
                  code: CONSTANTS.CODE.FAIL,
                  message: messageBlock
                })
              })

            return;
          }

          userInfDB = result;
          next();
        });
    }

    const handleRegisterService = (next) => {
      OrderType
        .find({move: {$ne: 1}, hireDriver: {$ne:1}, care: {$ne:1}}, '_id')
        .lean()
        .exec((err, result) => {
          if (err || !result) {
            return next(err || new Error(`Not found order type info`));
          }

          const orderTypeId = result.map(orderType => orderType._id.toHexString());
          Rider.count({member: userInfDB._id}, (err, count) => {
            if (count > 0) {
              return next();
            }

            Rider.create({
              member: userInfDB._id,
              serviceRunning: service.order.concat(orderTypeId)
            }, (err, result) => {
              if(err) {
                return next(err);
              }
              next();
            })
          })
        })
    }

    const handleRegisterServiceHeyCare = (next) => {
      if(appName !== 'staff') {
        return next();
      }
      OrderType
        .find({ care: 1 }, '_id')
        .lean()
        .exec((err, result) => {
          if (err || !result) {
            return next(err || new Error(`Not found order type info`));
          }

          const orderTypeId = result.map(orderType => orderType._id.toHexString());
          StaffHeyCare.count({ member: userInfDB._id }, (err, count) => {
            if (count > 0) {
              return next();
            }

            StaffHeyCare.create({
              member: userInfDB._id,
              serviceRunning: service.heyCare.concat(orderTypeId)
            }, (err, result) => {
              if (err) {
                return next(err);
              }

              next();
            })
          })
        })
    }

    const makeSingleLogin = (next) => {
      const userId = userInfDB._id.toHexString();

      let stringUser = `user:${userId}`

      if (appName) {
        stringUser = `${appName}:${userId}`;

        Members
          .count({
            _id: userId,
            type: 0,
            appName: 'customer'
          })
          .exec((err,count) => {
            if(!err && count) {
              redisConnection.get(`user:${userId}`, (err, tokenDel) => {
                redisConnection.del(`user:${tokenDel}`, (err, result) => {
                });
              })
            }
          })
      }

      redisConnection.get(stringUser, (err, token) => {
        if(err) {
          return next(err);
        }

        if(token) {

          let stringToken = `user:${token}`

          if (appName) {
            stringToken = `${appName}:${token}`;
          }

          redisConnection.del(stringToken, (err, result) => {
            if(err) {
              return next(err);
            }

            next();
          });
        } else {
          next();
        }
      });
    }

    const getToken = (next) => {

      redisConnection.get(`user:${userInfDB._id.toHexString()}`, (err, token) => {
        tokenRedis = token;

        next();
      });
    }

    const updateMemberHeywow = (next) => {

      if(appName !== 'heywow') {
        return next();
      }

      HeyWowMember
        .update({
          phone: userInfDB.phone,
          status:1
        },{
          phone: null,
          member: userInfDB._id
        },() => {
          next();
        })
    }

    const handleHeywow = (next) => {
      if(appName !== 'heywow') {
        return next();
      }
      HeyWowMember
        .findOne({
          member: userInfDB._id,
          status:1
        })
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err)
          }
          if(result) {
            objWow.department = result.department || '';
            objWow.roles = result.roles || []
            objWow.totalOrder = result.totalOrder;
            objWow.membership = result.membership
          }
          next();
        })
    }

    const handleHeyCarePartner = (next) => {
      if(appName !== 'staff') {
        return next();
      }
      PartnerHeyCare
        .count({
          member: userInfDB._id,
          status:1
        })
        .exec((err, count) => {
          if(err) {
            return next(err)
          }
          if(count) {
            isPartnerHeyCare = 1
          }
          next();
        })
    }

    const generateToken = (next) => {
      const userId = userInfDB._id.toHexString();
      const objSign = {
        id: userId,
        accessToken: _.get(req, 'body.access_token', ''),
        platform: req.body.platform || 'unknown',
        refreshAt: Date.now()
      }
      if(objWow.department && objWow.roles) {
        objSign.department = objWow.department;
        objSign.roles = objWow.roles;
      }

      let stringUser = `user:${userId}`
      let stringToken = `user:${memberToken}`

      if (appName) {
        stringUser = `${appName}:${userId}`
        stringToken = `${appName}:${memberToken}`
      }

      const fnc =
        redisConnection
          .multi()
          .set(stringToken, JSON.stringify(objSign))
          .set(stringUser, memberToken)

      fnc
        .exec((err, result) => {
          if(err) {
            return next(err);
          }

          redisConnection.del(`otp:${phone}`);

          const data = _.merge({}, userInfDB, {memberToken});
          _.unset(data, 'facebook.token');
          _.unset(data, 'password');
          data.isExpire = (data.expireTime < Date.now());

          // publish event
          if(Date.now() - data.createdAt <= 30000) {
            redisConnectionForPubSub.publish("new_user", data._id.toHexString());
          }
          memberResult = data
          next();
        });
    }

    const pushToOldDevice = (next) => {

      if(objWow.department && objWow.roles) {
        memberResult.department = objWow.department;
        memberResult.roles = objWow.roles;
        memberResult.membership = objWow.membership;
        memberResult.totalOrder = objWow.totalOrder;
      }
      memberResult.isPartnerHeyCare = isPartnerHeyCare
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        member: memberResult
      });

      TrackingAction
        .find({
          member: memberResult._id
        })
        .sort('-createdAt')
        .limit(1)
        .lean()
        .exec((err, results) => {

          if(err) {
            return;
          }

          if(!results.length) {
            return;
          }

          if(results[0].otherInf && results[0].otherInf.uniqueId && deviceId !== results[0].otherInf.uniqueId && !fromPartner) {
            let member = results[0].member
            const options = {
              method: 'POST',
              uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
              body: {
                  userId: member,
                  title: "Thông báo",
                  appName,
                  message: `Tài khoản của bạn đã đăng nhập vào thiết bị khác. Vui lòng kiểm tra lại nếu bạn không thực hiện hành động này. Liên hệ Hotline 1900.633.689 để được hỗ trợ.`
              },
              json: true
            };

            rp(options)
              .then((result) => {
              })
              .catch((err) => {
              });
          }

        })
    }

    async.waterfall([
      checkParams,
      getPhoneFromToken,
      checkMemberExists,
      createIfNotExists,
      updateIfExits,
      handleRegisterService,
      handleRegisterServiceHeyCare,
      makeSingleLogin,
      getToken,
      updateMemberHeywow,
      handleHeywow,
      handleHeyCarePartner,
      generateToken,
      pushToOldDevice
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: message.SYSTEM.ERROR
      });

      res.json(data || err);
    });
  }
}
