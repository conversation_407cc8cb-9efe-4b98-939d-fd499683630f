import _ from 'lodash';
import async from 'async';
import util from 'util';
import fs from 'fs.extra'
import { amazonS3Addr, notifyServiceAddr, locationHeyCareAddr, service, orderType, orderTypeGroup, weatherApiKey } from '../../config';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import uuid from 'uuid/v4';
import rp from 'request-promise'
var Util = require('../../Util');
import * as mailUtil from '../../mailUtil';
const ms = require('ms')
import crypto from 'crypto';
// import geoip from 'geoip-lite';

export default {
  listServiceRegisterHeyCare(req, res) {
    const { regionName } = req.body;
    const id = req.user.id;

    let services;

    const checkParams = (next) => {
      if (!regionName || !id) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        })
      }

      next();
    }

    const listServiceAvailable = (next) => {
      OrderType
        .find({
          status: 1,
          showListStaff: 1,
          $or: [
            {
              'region.allow': regionName
            },
            {
              'region.allow': 'all',
              'region.deny': {
                $ne: regionName
              }
            }
          ]
        })
        .select({
          name: 1,
          icon: 1,
          editable: 1,
          messageForRider: 1,
          canRegister: 1,
          serviceRaw: 1
        })
        .sort('-order')
        .lean()
        .exec((err, results) => {
          if (err) {
            return next(err)
          }

          services = results;
          services.map(item => {
            if (service.heyCare.includes(item.serviceRaw.toHexString())) {
              item.service = service.heyCare;
            }
            else if (service.heyClean.includes(item.serviceRaw.toHexString())) {
              item.service = service.heyClean;
            }
          })

          next();
        })
    }

    const findRunningService = (next) => {
      StaffHeyCare
        .findOne({
          member: id
        })
        .lean()
        .select({
          serviceRunning: 1,
          serviceRegisted: 1
        })
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (!result || !result.serviceRunning) {
            return next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                services,
                count: 0
              }
            })
          }

          let count = 0;
          const serviceRunning = result.serviceRunning.map(String)

          services.some((service) => {
            if (serviceRunning.includes(service._id.toString())) {
              service.active = 1;
              count++;
            }
          })

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              services,
              count
            }
          })
        })
    }

    async.waterfall([
      checkParams,
      listServiceAvailable,
      findRunningService
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      res.json(data || err);
    });
  },
  listServiceAvailable(req, res) {
    const region = req.body.regionName;
    const mode = req.body.modeApp;
    const userId = req.user.id;
    const platform = req.body.platform;
    const nativeVersion = req.body.nativeVersion;
    const appName = req.body.appName;
    let blackArray = [];

    let isOrderActive = true
    let memberInf
    let showHub

    const checkParams = (next) => {
      if (!region || !userId || !platform || !nativeVersion) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        });
      }

      next();
    }

    const findUser = (next) => {
      if (region !== 'hn' && region !== 'vietnam:danang') {
        return next();
      }

      Members
        .findOne({
          _id: userId
        })
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err)
          }

          if (!result) {
            return next('not found member')
          }

          memberInf = result
          if (result.staff && result.staff.isAuthen) {
            isOrderActive = false
          }

          next();
        })
    }

    const findOrder = (next) => {
      if (!isOrderActive || region !== 'hn') {
        return next();
      }

      OrderSystem
        .count({
          shop: userId,
          status: { $in: [3, 10] }
        })
        .limit(2)
        .lean()
        .exec((err, count) => {
          if (err) {
            return next(err)
          }

          if (count < 2) {
            isOrderActive = false
          }

          next();
        })
    }

    const lisService = (next) => {
      Service.list(region, platform, nativeVersion, appName, (err, data) => {
        if (err) {
          return next(err);
        }

        data.forEach((item, i) => {
          // if(item._id.toHexString() === '6096369fa9b6fb64b4b7fddd' && userId !== '6063e806e55d457209ac0e6e') {
          //   if(mode !== 'shipper' && userId !== '6063e806e55d457209ac0e6e') {
          //     data.splice(i,1)
          //   }
          // }

          if (item.configTags && item.configTags[region]) {
            if (item.configTags[region].tagImage) {
              item.tagImage = item.configTags[region].tagImage
            }

            if (item.configTags[region].tag) {
              item.tag = item.configTags[region].tag
            }
          }

          let extras = item.extras || {}
          if (item.promote && item.promote[region]) {
            extras.promoteId = item.promote[region];
            item.extras = extras
          }

          if (item.timeActive && item.timeActive[region]) {
            const timeActive = item.timeActive[region];

            const today = new Date();
            const startDate = today.setHours(0, 0, 0, 0)
            const currentHourInMs = Date.now() - startDate;

            if (timeActive.from && timeActive.to && (currentHourInMs < timeActive.from || currentHourInMs > timeActive.to)) {
              item.active = 0;
              item.message = timeActive.message
            }
          }

          if (blackArray.includes(item._id.toHexString())) {
            item.blockCreate = 1
          }

        })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data
        })
      })
    }

    async.waterfall([
      checkParams,
      // findUser,
      //findOrder,
      lisService
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      res.json(data || err);
    });
  },
  switchServiceHeyCare(req, res) {
    const id = req.user.id;
    const serviceId = req.body.service;
    let servicesUpdate = []
    let groupService = [serviceId]

    const checkParams = (next) => {
      if (!id || !serviceId) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        })
      }

      // if (orderTypeGroup) {
      //   Object.keys(orderTypeGroup).forEach((orderType, i) => {
      //     if (orderTypeGroup[orderType].includes(serviceId)) {
      //       groupService = orderTypeGroup[orderType]
      //     }
      //   });
      // }

      next();
    }

    const findRiderInfo = (next) => {
      StaffHeyCare
        .findOne({
          member: id
        })
        .lean()
        .select({
          serviceRunning: 1
        })
        .exec((err, result) => {
          if (err) {
            return next(err)
          }

          if (!result || !result.serviceRunning) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGE.SYSTEM.ERROR
            })
          }

          const serviceRunning = result.serviceRunning.map(String)

          const index = serviceRunning.indexOf(serviceId)
          if (index > -1) {
            for (let i = 0; i < serviceRunning.length; i++) {
              if (groupService.includes(serviceRunning[i])) {
                serviceRunning.splice(i, 1);
                i--;
              }
            }

            servicesUpdate = serviceRunning
          } else {
            groupService.forEach((item, i) => {
              if (!serviceRunning.includes(item)) {
                serviceRunning.push(item)
              }
            });

            servicesUpdate = serviceRunning;
          }

          next();
        })
    }

    const switchService = (next) => {
      let objUpdate = {};
      objUpdate = {
        serviceRunning: servicesUpdate,
        updatedAt: Date.now()
      }

      StaffHeyCare
        .findOneAndUpdate({
          member: id
        }, objUpdate, {
          new: true
        })
        .select('serviceRunning')
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err)
          }

          const dataRedis = JSON.stringify(result)
          redisConnection.set(`cacheman:cachegoose-cache:staff:${id}`, dataRedis, (err, res) => { });

          const serviceRunning = result.serviceRunning.map(String)
          const options = {
            method: 'POST',
            uri: `${locationHeyCareAddr}/api/v1.0/staff/update-service`,
            body: {
              id,
              services: serviceRunning
            },
            json: true
          }

          rp(options)
            .then((result) => {
              if (result.code !== 200) {
                return next({
                  code: CONSTANTS.CODE.FAIL,
                  message: MESSAGE.SYSTEM.ERROR
                })
              }

              next(null, {
                code: CONSTANTS.CODE.SUCCESS,
                data: serviceRunning
              })
            })
            .catch((err) => {
              mailUtil
                .sendMail(` --- ERR switchService ${locationHeyCareAddr}/api/v1.0/staff/update-service --- ${err}`);
              return next(err);
            })
        })
    }

    async.waterfall([
      checkParams,
      findRiderInfo,
      switchService
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      res.json(data || err);
    });
  },
  getConfigForUpdateLocation(req, res) {
    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        interval: 60000,
        options: {
          enableHighAccuracy: true,
          timeout: 20000
        },
        url: `${locationHeyCareAddr}/api/v1.0/staff/update-latest-location`
      }
    })
  },
  getConfigForBackgroundLocation(req, res) {
    let userInf;

    const getAuthenInf = (next) => {
      Members
        .findById(req.user.id, "staff.isAuthen")
        .lean()
        .exec((err, result) => {
          userInf = result;

          next();
        })
    }

    const getConfigInf = (next) => {
      const platform = req.body.platform || '';

      const config = {
        locationProvider: platform === 'android' ? "ACTIVITY_PROVIDER" : "DISTANCE_FILTER_PROVIDER",
        desiredAccuracy: "HIGH_ACCURACY",
        stationaryRadius: 50,
        debug: false,
        distanceFilter: 200,
        notificationTitle: 'Đang sử dụng vị trí của bạn',
        notificationText: 'Việc sử dụng vị trí của bạn giúp chúng tôi có thể thông báo đơn hàng cho bạn khi bạn không sử dụng ứng dụng',
        notificationIconSmall: 'ic_bg_launcher',
        notificationIconLarge: 'ic_bg_launcher',
        notificationIconColor: '#00eaaf',
        notificationsEnabled: false,
        startForeground: false,
        stopOnTerminate: false,
        fastestInterval: 30000,
        interval: 60000,
        activityType: "AutomotiveNavigation",
        pauseLocationUpdates: false,
        saveBatteryOnBackground: false,
        syncThreshold: 25,
        maxLocations: 200,
        serverUrl: `${locationHeyCareAddr}/api/v1.0/staff/update-location`,
        serverSyncUrl: `${locationHeyCareAddr}/api/v1.0/staff/sync-update-location`,
        httpHeaders: {},
        postTemplate: {
          lat: "@latitude",
          lng: "@longitude",
          speed: "@speed",
          bearing: "@bearing",
          updatedAt: "@time"
        }
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: config
      })
    }

    async.waterfall([
      // getAuthenInf,
      getConfigInf
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      res.json(data || err);
    })
  },
  getConfigAuthenHeyCare(req, res) {
    const platform = req.body.platform || '';
    const nativeVersion = req.body.nativeVersion || '';
    const appName = req.body.appName || 'customer';
    const regionName = req.body.regionName || 'unknown';
    const userId = req.user.id;
    let staffAuthenInf;

    const checkParams = (next) => {
      if (!nativeVersion || !appName || !regionName) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        })
      }

      next();
    }

    const checkMember = (next) => {
      CaregiverAuthentication
        .findOne({ member: userId }, 'onlineAuthen')
        .lean()
        .exec((err, result) => {
          if (result && !result.onlineAuthen) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                online: 0,
                offline: 1
              }
            })
          }

          staffAuthenInf = result;

          next();
        })
    }

    const getConfig = (next) => {
      Config.get(CONSTANTS.CONFIG_TYPE.AUTHEN_HEYCARE, regionName, (err, data) => {
        if (err) {
          return next(err);
        }

        if (!data || !data.config) {
          return next(new Error('Invalid config'));
        }

        let result = {
          code: CONSTANTS.CODE.SUCCESS,
          data: data.config
        }

        next(null, result);
      })
    }

    async.waterfall([
      checkParams,
      checkMember,
      getConfig
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      res.json(data || err);
    });
  },
  getConfigBlockStaff(req, res) {
    const regionName = req.body.regionName || '';
    const userId = req.user.id || '';

    let member
    let configData;

    const checkParams = (next) => {
      if (!userId || !regionName) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        })
      }

      next();
    }

    const getConfig = (next) => {
      Config.get(CONSTANTS.CONFIG_TYPE.BLOCK_STAFF_ONLINE, regionName, (err, dataConfig) => {
        if (err || !dataConfig) {
          return next(err || new Error('Config not found'));
        }

        let data = dataConfig.config || ''
        if (!data || (data && !data.isOpen)) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              isOpen: 0
            }
          })
        }

        configData = data

        next()
      })
    }

    const checkAccountBlock = (next) => {
      Members
        .findOne({
          _id: userId
        })
        .select('blockOrderUtil training')
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (!result) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
            })
          }

          member = result
          if (member.blockOrderUtil < Date.now()) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                isOpen: 0
              }
            })
          }

          next();
        })
    }

    const showInfo = (next) => {
      BlockLog
        .find({
          member: userId,
          blockType: 'blockOrderUtil',
          status: -1,
          'data.timeBlockUtil': member.blockOrderUtil
        })
        .sort('-createdAt')
        .limit(1)
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err)
          }

          if (!result.length) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                isOpen: 0
              }
            })
          }

          if (!configData.online) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                isOpen: 1,
                online: 0,
                message: result[0].message,
                description: result[0].description,
                blockInf: {
                  _id: result[0]._id,
                  blockOrderUtil: member.blockOrderUtil
                }
              }
            })
          }

          if (!result[0].reTraining) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                isOpen: 0
              }
            })
          }

          if (member.training && result[0].reTraining) {

            Members
              .update({
                _id: userId
              }, {
                training: 0
              }, () => {
              })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              isOpen: 1,
              online: 1,
              description: result[0].description,
              message: result[0].message,
              blockInf: {
                _id: result[0]._id,
                blockOrderUtil: member.blockOrderUtil,
                amount: result[0].amount,
                reTraining: result[0].reTraining,
                statusTraining: result[0].statusTraining,
                statusPayment: result[0].statusPayment
              }
            }
          })
        })
    }

    async.waterfall([
      checkParams,
      getConfig,
      checkAccountBlock,
      showInfo
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      res.json(data || err);
    });
  },
  getContactHeyCare(req, res) {
    const deviceInf = _.get(req, 'body.device', '')

    Config
      .getData({
        type: CONSTANTS.CONFIG_TYPE.CONTACT_HEYCARE,
        region: req.body.regionName,
        regionDistrict: req.user.regionDistrict || ''
      }, (err, data) => {
        if (err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }

        if (data && data.config && deviceInf) {
          data.config.canCallNormal = true
          if (deviceInf && data.config.blackListDevices) {
            data.config.blackListDevices.forEach((blackDevice) => {
              if (deviceInf.deviceBrand.includes(blackDevice)) {
                data.config.canCallNormal = false;
              }
            })
          }
        }

        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: data ? data.config : null
        })
      })
  },
  checkCanTurnOnPushOrder(req, res) {
    const userId = req.user.id;
    const regionName = req.body.regionName;
    let userInf;

    const checkParams = (next) => {
      if (!regionName) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        })
      }

      next();
    }

    const getUserInf = (next) => {
      Members
        .findById(userId)
        .lean()
        .exec((err, result) => {
          if (err || !result) {
            return next(err || new Error(`Not found user info`))
          }

          userInf = result;

          next();
        });
    }

    const checkCoint = (next) => {
      Config.get(CONSTANTS.CONFIG_TYPE.COINTS_STAFF, regionName, (err, data) => {
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.FAIL
          })
        }

        if (userInf.cointsStaff <= data.config.minCoints) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              canTurnOnPushOrder: 0
            },
            message: {
              head: 'Thông báo',
              body: 'Tài khoản của bạn hiện tại nhỏ hơn số dư tối thiểu. Bạn vui lòng nạp thêm để có thể bật chức năng nhận đơn hệ thống. Xin cảm ơn!'
            }
          })
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            canTurnOnPushOrder: 1
          }
        })
      })
    }

    async.waterfall([
      checkParams,
      getUserInf,
      checkCoint
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      res.json(data || err);
    });
  },
  trackingHeyCare(req, res) {
    res.json({
      code: 200
    });

    const member = req.user.id;

    const obj = {
      member
    }

    TrackingHeyCare
      .count({ member })
      .lean()
      .exec((err, count) => {
        if (!count) {
          TrackingHeyCare.create(obj, (err, result) => { });
        }
      })
  },
  updateIdentityBlockShipper(req, res) {
    const region = req.body.regionName || '';
    const identityFont = req.body.identityFont || '';
    const identityBack = req.body.identityBack || '';
    const avatarWithUniform = req.body.avatarWithUniform || '';
    const userId = req.user.id || '';

    let member, blockLog

    const checkParams = (next) => {
      if (!identityFont || !identityBack || !avatarWithUniform) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        })
      }

      next();
    }

    const checkAccountBlock = (next) => {
      Members
        .findOne({
          _id: userId
        })
        .select('blockOrderUtil')
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (!result) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }

          if (result.blockOrderUtil < Date.now()) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          member = result

          next();
        })
    }

    const updateInfo = (next) => {
      BlockLog
        .findOneAndUpdate({
          member: userId,
          blockType: 'blockOrderUtil',
          status: -1,
          'data.timeBlockUtil': member.blockOrderUtil
        }, {
          identityFont, identityBack, avatarWithUniform, statusInfo: 0
        }, { new: true })
        .sort('-createdAt')
        .lean()
        .exec((err, result) => {
          if (err || !result) {
            return next(err || new Error('BlockLog not found'))
          }

          blockLog = result;

          next()
        })
    }

    const createRequest = (next) => {
      next(null, {
        code: CONSTANTS.CODE.SUCCESS
      })

      ReqUnlockMember
        .create({
          member: userId,
          amount: blockLog.amount,
          reTraining: blockLog.reTraining,
          type: blockLog.type,
          blockType: blockLog.blockType,
          requester: userId,
          status: 1,
          identityFont,
          identityBack,
          avatarWithUniform,
          region
        })
    }

    async.waterfall([
      checkParams,
      checkAccountBlock,
      updateInfo,
      createRequest
    ], (err, data) => {
      console.log('haha:err', err, data)
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      res.json(data || err);
    });
  },
  // Helper function để gọi Open-Meteo API làm backup
  async getWeatherFromOpenMeteo(lat, lng) {
    const OPEN_METEO_URL = "https://api.open-meteo.com/v1/forecast";

    const options = {
      uri: OPEN_METEO_URL,
      qs: {
        latitude: lat,
        longitude: lng,
        current: 'temperature_2m,weather_code',
        timezone: 'Asia/Ho_Chi_Minh'
      },
      json: true,
    };

    try {
      const data = await rp(options);
      if (!data || !data.current) {
        throw new Error('Invalid response from Open-Meteo');
      }

      // Map weather code to condition text (simplified mapping)
      const weatherCodeMap = {
        0: 'Trời quang',
        1: 'Chủ yếu quang đãng',
        2: 'Một phần có mây',
        3: 'Nhiều mây',
        45: 'Sương mù',
        48: 'Sương mù đóng băng',
        51: 'Mưa phùn nhẹ',
        53: 'Mưa phùn vừa',
        55: 'Mưa phùn nặng',
        61: 'Mưa nhẹ',
        63: 'Mưa vừa',
        65: 'Mưa to',
        80: 'Mưa rào nhẹ',
        81: 'Mưa rào vừa',
        82: 'Mưa rào to',
        95: 'Dông',
        96: 'Dông có mưa đá nhẹ',
        99: 'Dông có mưa đá nặng'
      };

      const condition = weatherCodeMap[data.current.weather_code] || 'Không xác định';

      // Generate a simple icon URL based on weather code
      let iconCode = 'unknown';
      if (data.current.weather_code === 0) iconCode = 'sunny';
      else if ([1, 2].includes(data.current.weather_code)) iconCode = 'partly-cloudy';
      else if (data.current.weather_code === 3) iconCode = 'cloudy';
      else if ([51, 53, 55, 61, 63, 65, 80, 81, 82].includes(data.current.weather_code)) iconCode = 'rainy';
      else if ([95, 96, 99].includes(data.current.weather_code)) iconCode = 'thunderstorm';
      else if ([45, 48].includes(data.current.weather_code)) iconCode = 'fog';

      return {
        temperature: Math.round(data.current.temperature_2m),
        condition: condition,
        icon: `https://cdn.weatherapi.com/weather/64x64/day/${iconCode === 'unknown' ? '116' : iconCode === 'sunny' ? '113' : iconCode === 'partly-cloudy' ? '116' : iconCode === 'cloudy' ? '119' : iconCode === 'rainy' ? '296' : iconCode === 'thunderstorm' ? '200' : '248'}.png`
      };
    } catch (error) {
      console.error("Lỗi khi gọi Open-Meteo API:", error.message);
      throw error;
    }
  },

  async getWeather(req, res) {
    const region = req.body.regionName;
    const address = req.body.address;
    const location = req.body.location;
    const WEATHER_API_KEY = weatherApiKey;
    const WEATHER_API_URL = "http://api.weatherapi.com/v1/current.json";

    if (!region || !address || !location || !location.lat || !location.lng) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    const cacheKey = `weather:${location.lat},${location.lng}`;
    try {
      redisConnection.get(cacheKey, async (err, cachedData) => {
        if (err) {
          console.error('Redis get error:', err);
        }
        if (cachedData) {
          // Nếu có cache, trả về luôn
          return res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: JSON.parse(cachedData)
          });
        }

        // Không có cache, gọi API
        const options = {
          uri: WEATHER_API_URL,
          qs: {
            key: WEATHER_API_KEY,
            q: `${location.lat},${location.lng}`,
            lang: 'vi'
          },
          json: true,
        };

        let result = null;
        let apiUsed = 'WeatherAPI';

        try {
          // Thử gọi WeatherAPI trước
          const data = await rp(options);
          if (!data || !data.current) {
            throw new Error('Invalid response from WeatherAPI');
          }

          result = {
            temperature: data.current.temp_c,
            condition: data.current.condition.text,
            icon: `https:${data.current.condition.icon}`
          };

        } catch (weatherApiError) {
          console.error("Lỗi khi gọi WeatherAPI:", weatherApiError.message);
          console.log("Đang thử sử dụng Open-Meteo làm backup...");

          try {
            // Fallback sang Open-Meteo
            result = await this.getWeatherFromOpenMeteo(location.lat, location.lng);
            apiUsed = 'Open-Meteo (backup)';
            console.log("Đã lấy dữ liệu thành công từ Open-Meteo");
          } catch (openMeteoError) {
            console.error("Lỗi khi gọi Open-Meteo backup:", openMeteoError.message);
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            });
          }
        }

        if (!result) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }

        // Log API được sử dụng
        console.log(`Weather data retrieved from: ${apiUsed}`);

        // Lưu cache với thời gian sống 10 phút
        redisConnection.setex(cacheKey, 600, JSON.stringify(result), (err) => {
          if (err) {
            console.error('Redis setex error:', err);
          }
        });

        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        });
      });
    } catch (error) {
      console.error('Unexpected error in getWeather:', error);
      res.json({
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });
    }
  }
}
